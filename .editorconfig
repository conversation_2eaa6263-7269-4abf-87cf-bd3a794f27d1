# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

# Markdown files
[*.md]
trim_trailing_whitespace = false

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yaml,yml}]
indent_size = 2

# Shell scripts
[*.sh]
indent_style = tab
indent_size = 4

# Makefiles use real tabs
[Makefile]
indent_style = tab

# Batch files use CRLF line endings
[*.{cmd,bat}]
end_of_line = crlf

# Git commit messages
[.git/**]
charset = utf-8
indent_style = space
indent_size = 2
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = false

# Package.json files
[package.json]
indent_style = space
indent_size = 2

# TypeScript and JavaScript files
[*.{ts,tsx,js,jsx}]
indent_style = space
indent_size = 2

# CSS, SCSS, and PostCSS files
[*.{css,scss,pcss,postcss}]
indent_style = space
indent_size = 2

# HTML files
[*.{html,htm}]
indent_style = space
indent_size = 2

# XML files
[*.{xml,xsd,xslt}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yaml,yml}]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = tab
indent_size = 4

# Makefiles use real tabs
[Makefile]
indent_style = tab

# Batch files use CRLF line endings
[*.{cmd,bat}]
end_of_line = crlf
