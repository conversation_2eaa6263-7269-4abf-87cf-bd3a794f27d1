{"name": "stone-showcase", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --port 3344", "build": "tsc -b && vite build", "preview": "vite preview --port 3344", "start": "vite --port 3344", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\""}, "dependencies": {"@modelcontextprotocol/server-filesystem": "^2025.3.28", "@react-three/drei": "^9.99.0", "@react-three/fiber": "^8.15.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.29", "framer-motion": "^12.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-qr-code": "^2.0.15", "react-router-dom": "^6.28.0", "three": "^0.160.0"}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.160.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "vite": "^5.0.12", "vite-plugin-pwa": "^1.0.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "A modern, responsive e-commerce website showcasing premium stone and ceramic products with 3D visualization capabilities.", "main": ".eslintrc.js", "keywords": [], "author": "", "license": "ISC"}