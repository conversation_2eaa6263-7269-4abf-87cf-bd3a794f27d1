{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "target": "ES2022", "composite": true, "skipLibCheck": true, "module": "NodeNext", "moduleResolution": "NodeNext", "allowSyntheticDefaultImports": true, "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "noUnusedLocals": true, "noUnusedParameters": true, "erasableSyntaxOnly": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true}, "include": ["vite.config.ts"]}