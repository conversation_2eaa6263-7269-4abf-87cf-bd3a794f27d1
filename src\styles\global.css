@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #1A1A1A;
  --color-secondary: #2D2D2D;
  --color-accent: #B8860B;
  --color-highlight: #FFFFFF;
  --color-tertiary: #404040;
  --color-success: #4CAF50;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

@layer base {
  body {
    @apply bg-primary text-white font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-semibold;
  }

  a {
    @apply text-accent transition-colors duration-300 hover:text-accent/80;
  }

  button, .btn {
    @apply bg-accent text-primary font-semibold py-3 px-6 rounded-md transition-all duration-300 hover:bg-accent/90 hover:shadow-glow;
  }

  .container {
    @apply w-full max-w-7xl mx-auto px-4;
  }
}

@layer components {
  .btn-outline {
    @apply bg-transparent border-2 border-accent text-accent hover:bg-accent/10;
  }
  
  .card {
    @apply bg-secondary rounded-lg overflow-hidden shadow-lg transition-transform duration-300 hover:-translate-y-1;
  }
  
  .section {
    @apply py-16 md:py-24;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold mb-8 text-center;
  }
  
  .section-subtitle {
    @apply text-lg md:text-xl text-gray-300 max-w-3xl mx-auto text-center mb-12;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-accent/50 rounded-full hover:bg-accent/70;
}

/* Animations */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom utilities */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hover-scale {
  @apply transition-transform duration-300 hover:scale-105;
}

/* Responsive typography */
@screen md {
  .section-title {
    @apply text-5xl;
  }
  
  .section-subtitle {
    @apply text-xl;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }
  
  .section {
    padding: 4rem 0;
  }
  
  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .section {
    padding: 3rem 0;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }
  
  .section {
    padding: 2.5rem 0;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
}
