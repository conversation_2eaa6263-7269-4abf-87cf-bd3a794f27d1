"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_router_dom_1 = require("react-router-dom");
var framer_motion_1 = require("framer-motion");
var NotFound = function () {
    return (<div className="min-h-screen bg-primary pt-24 pb-12 flex flex-col items-center justify-center px-4">
      <framer_motion_1.motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="text-center">
        <div className="text-9xl font-bold text-accent mb-4">404</div>
        <h1 className="text-4xl font-bold mb-4">Page Not Found</h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl">
          Oops! The page you're looking for doesn't exist or has been moved.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <react_router_dom_1.Link to="/" className="px-6 py-3 bg-accent text-primary font-medium rounded-md hover:bg-opacity-90 transition-colors">
            Go to Homepage
          </react_router_dom_1.Link>
          <react_router_dom_1.Link to="/contact" className="px-6 py-3 border border-accent text-accent font-medium rounded-md hover:bg-accent hover:bg-opacity-10 transition-colors">
            Contact Support
          </react_router_dom_1.Link>
        </div>
      </framer_motion_1.motion.div>
      
      <framer_motion_1.motion.div className="mt-16 text-center" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3, duration: 0.5 }}>
        <p className="text-gray-500 mb-2">Or try these pages:</p>
        <div className="flex flex-wrap justify-center gap-4">
          {[
            { name: 'Products', path: '/products' },
            { name: '3D Visualization', path: '/visualization' },
            { name: 'About Us', path: '/about' },
        ].map(function (item, index) { return (<react_router_dom_1.Link key={index} to={item.path} className="text-accent hover:underline">
              {item.name}
            </react_router_dom_1.Link>); })}
        </div>
      </framer_motion_1.motion.div>
    </div>);
};
exports.default = NotFound;
