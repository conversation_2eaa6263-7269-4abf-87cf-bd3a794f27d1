import { useState, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, useTexture } from '@react-three/drei';
import { motion } from 'framer-motion';

// 3D Model Components
const KitchenModel = ({ material }: { material: { color?: string; roughness?: number; metalness?: number; map?: any; normalMap?: any; roughnessMap?: any; } }) => {
  return (
    <group>
      {/* Countertop */}
      <mesh position={[0, 1, 0]} castShadow receiveShadow>
        <boxGeometry args={[3, 0.1, 1.5]} />
        <meshStandardMaterial map={material.map} normalMap={material.normalMap} roughnessMap={material.roughnessMap} color={material.color} roughness={material.roughness} metalness={material.metalness} />
      </mesh>
      
      {/* Backsplash */}
      <mesh position={[0, 1.5, -0.75]} rotation={[Math.PI / 2, 0, 0]} castShadow>
        <planeGeometry args={[3, 1, 1]} />
        <meshStandardMaterial map={material.map} normalMap={material.normalMap} roughnessMap={material.roughnessMap} color={material.color} roughness={material.roughness} metalness={material.metalness} />
      </mesh>
      
      {/* Cabinets */}
      <mesh position={[0, 0.5, 0.6]} castShadow>
        <boxGeometry args={[2.8, 1, 0.7]} />
        <meshStandardMaterial color="#2d2d2d" />
      </mesh>
    </group>
  );
};

const StairModel = ({ material }: { material: { color?: string; roughness?: number; metalness?: number; map?: any; normalMap?: any; roughnessMap?: any; } }) => {
  return (
    <group>
      {[...Array(5)].map((_, i) => (
        <mesh key={i} position={[0, i * 0.2, i * 0.4]} castShadow>
          <boxGeometry args={[2, 0.1, 0.4]} />
          <meshStandardMaterial map={material.map} normalMap={material.normalMap} roughnessMap={material.roughnessMap} color={material.color} roughness={material.roughness} metalness={material.metalness} />
        </mesh>
      ))}
      
      {/* Railing */}
      <mesh position={[1.2, 0.5, 0.8]} castShadow>
        <boxGeometry args={[0.1, 1, 0.1]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>
      
      <mesh position={[-1.2, 0.5, 0.8]} castShadow>
        <boxGeometry args={[0.1, 1, 0.1]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>
    </group>
  );
};

const FloorModel = ({ material }: { material: { color?: string; roughness?: number; metalness?: number; map?: any; normalMap?: any; roughnessMap?: any; } }) => {
  return (
    <mesh rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
      <planeGeometry args={[10, 10, 1]} />
      <meshStandardMaterial map={material.map} normalMap={material.normalMap} roughnessMap={material.roughnessMap} color={material.color} roughness={material.roughness} metalness={material.metalness} />
    </mesh>
  );
};

const WallModel = ({ material }: { material: { color?: string; roughness?: number; metalness?: number; map?: any; normalMap?: any; roughnessMap?: any; } }) => {
  return (
    <mesh position={[0, 2, -2]} receiveShadow>
      <planeGeometry args={[10, 4, 1]} />
      <meshStandardMaterial map={material.map} normalMap={material.normalMap} roughnessMap={material.roughnessMap} color={material.color} roughness={material.roughness} metalness={material.metalness} />
    </mesh>
  );
};

// Scene component
const Scene = ({ model, material }: { model: string; material: { color?: string; roughness?: number; metalness?: number; textures?: { map: string; normalMap: string; roughnessMap: string; } } }) => {
  const [map, normalMap, roughnessMap] = material.textures ? useTexture([material.textures.map, material.textures.normalMap, material.textures.roughnessMap]) : [undefined, undefined, undefined];
  return (
    <Canvas shadows camera={{ position: [3, 3, 3], fov: 50 }}>
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
      
      <Suspense fallback={null}>
        {model === 'kitchen' && <KitchenModel material={{ ...material, map, normalMap, roughnessMap }} />}
        {model === 'stair' && <StairModel material={{ ...material, map, normalMap, roughnessMap }} />}
        {model === 'floor' && <FloorModel material={{ ...material, map, normalMap, roughnessMap }} />}
        {model === 'wall' && <WallModel material={{ ...material, map, normalMap, roughnessMap }} />}
        
        <Environment preset="city" />
        <OrbitControls enableZoom={true} enablePan={true} />
      </Suspense>
    </Canvas>
  );
};

// Material selector component
const MaterialSelector = ({
  materials,
  selectedMaterial,
  onSelectMaterial,
}: {
  materials: Array<{ id: string; name: string; thumbnail: string }>;
  selectedMaterial: string;
  onSelectMaterial: (id: string) => void;
}) => {
  return (
    <div className="grid grid-cols-4 gap-4 mt-4">
      {materials.map((mat) => (
        <button
          key={mat.id}
          onClick={() => onSelectMaterial(mat.id)}
          className={`p-2 rounded-md transition-colors ${
            selectedMaterial === mat.id ? 'ring-2 ring-accent' : 'hover:bg-gray-700'
          }`}
        >
          <div className="h-16 w-full mb-2 overflow-hidden rounded">
            <img
              src={mat.thumbnail}
              alt={mat.name}
              className="w-full h-full object-cover"
            />
          </div>
          <span className="text-sm">{mat.name}</span>
        </button>
      ))}
    </div>
  );
};

// Main component
const Visualization = () => {
  const [selectedModel, setSelectedModel] = useState('kitchen');
  const [selectedMaterial, setSelectedMaterial] = useState('marble-1');
  
  // Mock materials data
  const materials = [
    {
      id: 'marble-1',
      name: 'Carrara White',
      thumbnail: '/images/materials/carrara-white.jpg', // Placeholder thumbnail
      textures: {
        map: '/textures/marble_01_diff_4k.jpg',
        normalMap: '/textures/marble_01_nor_4k.jpg',
        roughnessMap: '/textures/marble_01_rough_4k.jpg',
      },
      color: '#F0F0F0', // Lighter grey for marble
      roughness: 0.4,
      metalness: 0.05,
    },
    {
      id: 'granite-1',
      name: 'Black Galaxy',
      thumbnail: '/images/materials/black-galaxy.jpg', // Placeholder thumbnail
      textures: {
        map: '/textures/granite_01_diff_4k.jpg',
        normalMap: '/textures/granite_01_nor_4k.jpg',
        roughnessMap: '/textures/granite_01_rough_4k.jpg',
      },
      color: '#202020', // Darker grey for granite
      roughness: 0.3,
      metalness: 0.15,
    },
    {
      id: 'slate-1',
      name: 'Rustic Slate',
      thumbnail: '/images/materials/rustic-slate.jpg', // Placeholder thumbnail
      textures: {
        map: '/textures/slate_01_diff_4k.jpg',
        normalMap: '/textures/slate_01_nor_4k.jpg',
        roughnessMap: '/textures/slate_01_rough_4k.jpg',
      },
      color: '#505050', // Mid-grey for slate
      roughness: 0.7,
      metalness: 0.0,
    },
    {
      id: 'travertine-1',
      name: 'Ivory Travertine',
      thumbnail: '/images/materials/ivory-travertine.jpg', // Placeholder thumbnail
      textures: {
        map: '/textures/travertine_01_diff_4k.jpg',
        normalMap: '/textures/travertine_01_nor_4k.jpg',
        roughnessMap: '/textures/travertine_01_rough_4k.jpg',
      },
      color: '#D4C9B6', // Warm beige for travertine
      roughness: 0.5,
      metalness: 0.05,
    },
  ];

  const selectedMatData = materials.find(m => m.id === selectedMaterial) || materials[0];

  return (
    <div className="min-h-screen bg-primary pt-24 pb-12">
      <div className="container mx-auto px-4">
        <motion.h1
          className="text-4xl md:text-5xl font-bold mb-4 text-center text-highlight"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Interactive 3D Visualization
        </motion.h1>
        <motion.p
          className="text-lg md:text-xl text-gray-400 mb-12 text-center max-w-3xl mx-auto"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Explore our premium stone products in various applications and customize materials to envision your perfect space.
        </motion.p>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <motion.div
            className="lg:col-span-2 bg-secondary rounded-xl overflow-hidden shadow-xl"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="h-[500px] w-full">
              <Scene
                model={selectedModel}
                material={selectedMatData}
              />
            </div>
          </motion.div>

          <motion.div
            className="bg-secondary rounded-xl p-8 shadow-xl flex flex-col"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h2 className="text-2xl font-semibold mb-6 text-highlight">Customize Your View</h2>
            
            <div className="mb-6">
              <h3 className="font-medium mb-3 text-gray-300">Select Application</h3>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { id: 'kitchen', name: 'Kitchen' },
                  { id: 'stair', name: 'Staircase' },
                  { id: 'floor', name: 'Floor' },
                  { id: 'wall', name: 'Wall' },
                ].map((model) => (
                  <button
                    key={model.id}
                    onClick={() => setSelectedModel(model.id)}
                    className={`py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                      selectedModel === model.id
                        ? 'bg-accent text-primary shadow-md'
                        : 'bg-gray-700 text-gray-200 hover:bg-gray-600 hover:text-white'
                    }`}
                  >
                    {model.name}
                  </button>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <h3 className="font-medium mb-3 text-gray-300">Select Material</h3>
              <MaterialSelector
                materials={materials}
                selectedMaterial={selectedMaterial}
                onSelectMaterial={setSelectedMaterial}
              />
            </div>

            <div className="bg-gray-800 p-5 rounded-lg mb-6">
              <h3 className="font-medium mb-2 text-gray-200">Material Details</h3>
              <p className="text-sm text-gray-400 mb-3">{selectedMatData.name}</p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>Roughness: {Math.round(selectedMatData.roughness * 100)}%</span>
                <span>Reflectivity: {Math.round(selectedMatData.metalness * 100)}%</span>
              </div>
            </div>

            <div className="mt-auto"> {/* Pushes controls and button to bottom */}
              <h3 className="font-medium mb-3 text-gray-300">Controls</h3>
              <ul className="text-sm text-gray-400 space-y-2">
                <li>• Left-click + drag to rotate the model</li>
                <li>• Right-click + drag to pan the view</li>
                <li>• Scroll to zoom in/out</li>
              </ul>
            </div>

            <button className="mt-8 w-full bg-accent text-primary font-bold py-3 px-6 rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl">
              Save This Design
            </button>
          </motion.div>
        </div>

        <div className="mt-16 bg-secondary rounded-xl p-8 shadow-xl">
          <h2 className="text-2xl font-semibold mb-8 text-center text-highlight">Design Tips for Your Project</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                title: 'Lighting Matters',
                description: 'Different lighting conditions can dramatically change how the stone appears. View in both natural and artificial light.',
                icon: '💡',
              },
              {
                title: 'Consider Scale',
                description: 'The same stone can look different at various scales. Zoom in to see textures and patterns clearly.',
                icon: '📏',
              },
              {
                title: 'Sample First',
                description: 'Always order physical samples to see the true color and texture before making a final decision.',
                icon: '🔍',
              },
            ].map((tip, i) => (
              <motion.div
                key={i}
                className="bg-gray-800 p-6 rounded-lg text-center border border-gray-700 hover:border-accent transition-colors duration-300"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.15, duration: 0.6 }}
                whileHover={{ scale: 1.03, boxShadow: '0 0 20px rgba(184, 134, 11, 0.4)' }}
              >
                <div className="text-4xl mb-4">{tip.icon}</div>
                <h3 className="font-bold text-xl mb-2 text-white">{tip.title}</h3>
                <p className="text-gray-400 text-base">{tip.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Visualization;
