"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_router_dom_1 = require("react-router-dom");
var framer_motion_1 = require("framer-motion");
var Navbar = function () {
    var _a = (0, react_1.useState)(false), isOpen = _a[0], setIsOpen = _a[1];
    var _b = (0, react_1.useState)(false), scrolled = _b[0], setScrolled = _b[1];
    var location = (0, react_router_dom_1.useLocation)();
    (0, react_1.useEffect)(function () {
        var handleScroll = function () {
            var isScrolled = window.scrollY > 50;
            if (isScrolled !== scrolled) {
                setScrolled(isScrolled);
            }
        };
        document.addEventListener('scroll', handleScroll);
        return function () {
            document.removeEventListener('scroll', handleScroll);
        };
    }, [scrolled]);
    var navLinks = [
        { name: 'Home', path: '/' },
        { name: 'Products', path: '/products' },
        { name: '3D Visualization', path: '/visualization' },
        { name: 'Contact', path: '/contact' },
    ];
    return (<header className={"fixed w-full z-50 transition-all duration-300 ".concat(scrolled ? 'bg-opacity-90 bg-primary shadow-lg py-2' : 'bg-transparent py-4')}>
      <div className="container mx-auto px-4 flex justify-between items-center">
        <react_router_dom_1.Link to="/" className="text-2xl font-bold text-accent">
          StoneCraft
        </react_router_dom_1.Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-8">
          {navLinks.map(function (link) { return (<react_router_dom_1.Link key={link.path} to={link.path} className={"relative px-2 py-1 text-sm font-medium transition-colors ".concat(location.pathname === link.path
                ? 'text-accent'
                : 'text-highlight hover:text-accent')}>
              {link.name}
              {location.pathname === link.path && (<framer_motion_1.motion.span layoutId="nav-underline" className="absolute left-0 -bottom-1 w-full h-0.5 bg-accent" initial={false} transition={{
                    type: 'spring',
                    bounce: 0.2,
                    duration: 0.6,
                }}/>)}
            </react_router_dom_1.Link>); })}
        </nav>

        {/* Mobile menu button */}
        <button className="md:hidden text-highlight focus:outline-none" onClick={function () { return setIsOpen(!isOpen); }} aria-label="Toggle menu">
          <div className="w-6 flex flex-col items-end space-y-1.5">
            <span className={"block h-0.5 bg-highlight transition-all duration-300 ".concat(isOpen ? 'w-6 rotate-45 translate-y-2' : 'w-6')}/>
            <span className={"block h-0.5 bg-highlight transition-all duration-300 ".concat(isOpen ? 'opacity-0' : 'w-5')}/>
            <span className={"block h-0.5 bg-highlight transition-all duration-300 ".concat(isOpen ? 'w-6 -rotate-45 -translate-y-2' : 'w-4')}/>
          </div>
        </button>
      </div>

      {/* Mobile Navigation */}
      <framer_motion_1.AnimatePresence>
        {isOpen && (<framer_motion_1.motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: 'auto' }} exit={{ opacity: 0, height: 0 }} transition={{ duration: 0.3 }} className="md:hidden overflow-hidden bg-primary bg-opacity-95">
            <nav className="flex flex-col space-y-4 p-4">
              {navLinks.map(function (link) { return (<react_router_dom_1.Link key={link.path} to={link.path} className={"px-4 py-2 text-lg font-medium transition-colors ".concat(location.pathname === link.path
                    ? 'text-accent'
                    : 'text-highlight hover:text-accent')} onClick={function () { return setIsOpen(false); }}>
                  {link.name}
                </react_router_dom_1.Link>); })}
            </nav>
          </framer_motion_1.motion.div>)}
      </framer_motion_1.AnimatePresence>
    </header>);
};
exports.default = Navbar;
