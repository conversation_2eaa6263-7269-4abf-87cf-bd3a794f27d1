"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_router_dom_1 = require("react-router-dom");
var react_1 = require("react");
var react_helmet_async_1 = require("react-helmet-async");
var Navbar_1 = require("./components/Navbar");
var Footer_1 = require("./components/Footer");
require("./styles/global.css");
// Lazy load pages
var Home = (0, react_1.lazy)(function () { return Promise.resolve().then(function () { return require('./pages/Home'); }); });
var Products = (0, react_1.lazy)(function () { return Promise.resolve().then(function () { return require('./pages/Products'); }); });
var ProductDetail = (0, react_1.lazy)(function () { return Promise.resolve().then(function () { return require('./pages/ProductDetail'); }); });
var Visualization = (0, react_1.lazy)(function () { return Promise.resolve().then(function () { return require('./pages/Visualization'); }); });
var Contact = (0, react_1.lazy)(function () { return Promise.resolve().then(function () { return require('./pages/Contact'); }); });
var NotFound = (0, react_1.lazy)(function () { return Promise.resolve().then(function () { return require('./pages/NotFound'); }); });
function App() {
    return (<react_helmet_async_1.HelmetProvider>
      <div className="flex flex-col min-h-screen bg-primary text-white">
        <Navbar_1.default />
        <main className="flex-grow">
          <react_1.Suspense fallback={<div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-accent"></div>
              </div>}>
            <react_router_dom_1.Routes>
              <react_router_dom_1.Route path="/" element={<Home />}/>
              <react_router_dom_1.Route path="/products" element={<Products />}/>
              <react_router_dom_1.Route path="/products/:id" element={<ProductDetail />}/>
              <react_router_dom_1.Route path="/visualization" element={<Visualization />}/>
              <react_router_dom_1.Route path="/contact" element={<Contact />}/>
              <react_router_dom_1.Route path="*" element={<NotFound />}/>
            </react_router_dom_1.Routes>
          </react_1.Suspense>
        </main>
        <Footer_1.default />
      </div>
    </react_helmet_async_1.HelmetProvider>);
}
exports.default = App;
