import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-primary pt-24 pb-12 flex flex-col items-center justify-center px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <div className="text-9xl font-bold text-accent mb-4">404</div>
        <h1 className="text-4xl font-bold mb-4">Page Not Found</h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl">
          Oops! The page you're looking for doesn't exist or has been moved.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/"
            className="px-6 py-3 bg-accent text-primary font-medium rounded-md hover:bg-opacity-90 transition-colors"
          >
            Go to Homepage
          </Link>
          <Link
            to="/contact"
            className="px-6 py-3 border border-accent text-accent font-medium rounded-md hover:bg-accent hover:bg-opacity-10 transition-colors"
          >
            Contact Support
          </Link>
        </div>
      </motion.div>
      
      <motion.div 
        className="mt-16 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <p className="text-gray-500 mb-2">Or try these pages:</p>
        <div className="flex flex-wrap justify-center gap-4">
          {[
            { name: 'Products', path: '/products' },
            { name: '3D Visualization', path: '/visualization' },
            { name: 'About Us', path: '/about' },
          ].map((item, index) => (
            <Link
              key={index}
              to={item.path}
              className="text-accent hover:underline"
            >
              {item.name}
            </Link>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default NotFound;
