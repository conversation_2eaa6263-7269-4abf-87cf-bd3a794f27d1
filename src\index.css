@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  body {
    @apply bg-primary text-highlight font-sans antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-bold tracking-tight;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }
  
  p {
    @apply text-base md:text-lg leading-relaxed;
  }
  
  a {
    @apply text-accent hover:text-accent-400 transition-colors duration-200;
  }
  
  button, .btn {
    @apply px-6 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent;
  }
  
  .btn-primary {
    @apply bg-accent text-white hover:bg-accent-600;
  }
  
  .btn-secondary {
    @apply bg-secondary text-highlight hover:bg-secondary-700;
  }
  
  .btn-outline {
    @apply border-2 border-accent text-accent hover:bg-accent hover:text-white;
  }
}

/* Custom components */
@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section {
    @apply py-16 md:py-24 lg:py-32;
  }
  
  .card {
    @apply bg-secondary rounded-lg shadow-card hover:shadow-card-hover transition-shadow duration-300 overflow-hidden;
  }
  
  .input {
    @apply w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-accent focus:border-transparent;
  }
  
  .label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .text-shadow-md {
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  .text-shadow-lg {
    text-shadow: 8px 8px 16px rgba(0, 0, 0, 0.3);
  }
  
  .text-shadow-none {
    text-shadow: none;
  }
  
  .transform-center {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
  }
  
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
}

/* Animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .print\:w-full {
    width: 100% !important;
  }
  
  .print\:p-0 {
    padding: 0 !important;
  }
  
  .print\:m-0 {
    margin: 0 !important;
  }
}
