"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_router_dom_1 = require("react-router-dom");
var framer_motion_1 = require("framer-motion");
// Mock product data
var products = [
    {
        id: 1,
        name: 'Carrara Marble',
        category: 'natural-stone',
        price: 75.99,
        image: '/images/carrara-marble.jpg',
        description: 'Classic white and gray marble with subtle veining.',
        colors: ['white', 'gray']
    },
    {
        id: 2,
        name: 'Slate Tile',
        category: 'natural-stone',
        price: 45.50,
        image: '/images/slate-tile.jpg',
        description: 'Natural slate tiles with rich texture.',
        colors: ['black', 'gray', 'green']
    },
    // Add more products as needed
];
var Products = function () {
    var _a = (0, react_1.useState)(''), searchTerm = _a[0], setSearchTerm = _a[1];
    var _b = (0, react_1.useState)('all'), selectedCategory = _b[0], setSelectedCategory = _b[1];
    var filteredProducts = products.filter(function (product) {
        var matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.description.toLowerCase().includes(searchTerm.toLowerCase());
        var matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
        return matchesSearch && matchesCategory;
    });
    return (<div className="min-h-screen bg-primary pt-24 pb-12">
      <div className="container mx-auto px-4">
        <h1 className="text-4xl font-bold mb-8 text-center">Our Products</h1>
        
        {/* Search and Filter */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <input type="text" placeholder="Search products..." className="px-4 py-2 bg-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent" value={searchTerm} onChange={function (e) { return setSearchTerm(e.target.value); }}/>
          <select className="px-4 py-2 bg-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent" value={selectedCategory} onChange={function (e) { return setSelectedCategory(e.target.value); }}>
            <option value="all">All Categories</option>
            <option value="natural-stone">Natural Stone</option>
            <option value="ceramic-tiles">Ceramic Tiles</option>
            <option value="stair-stone">Stair Stone</option>
          </select>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map(function (product) { return (<framer_motion_1.motion.div key={product.id} className="bg-secondary rounded-lg overflow-hidden shadow-lg" whileHover={{ y: -5 }}>
              <react_router_dom_1.Link to={"/products/".concat(product.id)}>
                <div className="h-48 bg-gray-800 overflow-hidden">
                  <img src={product.image} alt={product.name} className="w-full h-full object-cover"/>
                </div>
                <div className="p-4">
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-gray-300 mb-2">${product.price.toFixed(2)}</p>
                  <p className="text-gray-400 text-sm">{product.description}</p>
                </div>
              </react_router_dom_1.Link>
            </framer_motion_1.motion.div>); })}
        </div>
      </div>
    </div>);
};
exports.default = Products;
