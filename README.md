# Stone Showcase - Premium Natural Stone & Ceramic Products

A modern, responsive e-commerce website showcasing premium stone and ceramic products with 3D visualization capabilities.

## 🚀 Features

- **Modern UI/UX** with dark mode and smooth animations
- **Responsive Design** that works on all devices
- **3D Product Visualization** with interactive models
- **Product Catalog** with filtering and search
- **Product Details** with high-resolution images and specifications
- **Contact Form** with validation
- **SEO Optimized** with proper meta tags and structured data
- **Progressive Web App (PWA)** support for offline access
- **Performance Optimized** with code splitting and lazy loading

## 🛠️ Tech Stack

- ⚡ [Vite](https://vitejs.dev/) - Next Generation Frontend Tooling
- ⚛️ [React 18](https://reactjs.org/) - JavaScript library for building user interfaces
- 📘 [TypeScript](https://www.typescriptlang.org/) - Typed JavaScript
- 🎨 [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- 🛣️ [React Router](https://reactrouter.com/) - Client-side routing
- 🎬 [Framer Motion](https://www.framer.com/motion/) - Animation library
- 📱 [React Icons](https://react-icons.github.io/react-icons/) - Popular icons
- 🌐 [React Helmet Async](https://github.com/staylor/react-helmet-async) - Document head management
- 🖼️ [Three.js](https://threejs.org/) - 3D library for the web
- 🏗️ [React Three Fiber](https://docs.pmnd.rs/react-three-fiber/getting-started/introduction) - React renderer for Three.js
- 📦 [Drei](https://github.com/pmndrs/drei) - Useful helpers for React Three Fiber

## 🚀 Getting Started

### Prerequisites

- Node.js 16+ and npm 8+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/stone-showcase.git
   cd stone-showcase
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Update the `.env` file with your configuration.

4. **Start the development server**
   ```bash
   npm run dev
   ```
   This will start the development server at `http://localhost:3000`.

## 📂 Project Structure

```
stone-showcase/
├── public/                  # Static files (images, fonts, etc.)
│   ├── favicon.ico         # Favicon
│   ├── favicon.svg         # SVG favicon
│   ├── robots.txt          # Robots configuration
│   └── site.webmanifest    # PWA manifest
├── src/
│   ├── assets/             # Static assets
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # Basic UI components
│   │   ├── layout/        # Layout components
│   │   └── shared/        # Shared components
│   ├── config/            # Application configuration
│   ├── constants/         # Constants and enums
│   ├── context/           # React context providers
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Third-party library configurations
│   ├── pages/             # Page components
│   │   ├── Home/          # Home page
│   │   ├── Products/      # Products listing page
│   │   ├── Product/       # Single product page
│   │   ├── Visualization/ # 3D visualization page
│   │   ├── Contact/       # Contact page
│   │   └── 404/          # 404 page
│   ├── services/          # API services
│   ├── styles/            # Global styles
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main application component
│   ├── main.tsx           # Application entry point
│   └── routes.tsx         # Application routes
├── .env                   # Environment variables
├── .env.example           # Example environment variables
├── .eslintrc.js           # ESLint configuration
├── .gitignore             # Git ignore file
├── index.html             # Main HTML template
├── package.json           # Project dependencies and scripts
├── postcss.config.js      # PostCSS configuration
├── README.md              # This file
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── tsconfig.node.json     # TypeScript configuration for Node
├── tsconfig.paths.json    # Path aliases configuration
└── vite.config.ts         # Vite configuration

## 📝 Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check code quality
- `npm run type-check` - Check TypeScript types
- `npm run format` - Format code with Prettier

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory and configure the following variables:

```env
# Application
VITE_APP_NAME="Stone Showcase"
VITE_APP_DESCRIPTION="Premium Natural Stone & Ceramic Products"
VITE_APP_VERSION=0.1.0

# API Configuration
VITE_API_URL=http://localhost:5000/api
VITE_API_TIMEOUT=10000

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ANALYTICS_ID=""

# External Services
VITE_MAPBOX_TOKEN=your_mapbox_token_here
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key

# Environment (development, staging, production)
NODE_ENV=development

# Build Configuration
GENERATE_SOURCEMAP=true

# PWA Configuration
VITE_PWA_ENABLED=true
VITE_PWA_REGISTER_TYPE=autoUpdate

# Logging
VITE_LOG_LEVEL=debug
```

## 🎨 Styling

This project uses [Tailwind CSS](https://tailwindcss.com/) for styling with the following customizations:

### Color Palette

| Name       | Hex       | RGB               |
|------------|-----------|-------------------|
| Primary    | `#1A1A1A` | `rgb(26, 26, 26)` |
| Secondary  | `#2D2D2D` | `rgb(45, 45, 45)` |
| Accent     | `#B8860B` | `rgb(184, 134, 11)`|
| Highlight  | `#FFFFFF` | `rgb(255, 255, 255)`|
| Tertiary   | `#404040` | `rgb(64, 64, 64)` |
| Success    | `#4CAF50` | `rgb(76, 175, 80)` |
| Error      | `#EF4444` | `rgb(239, 68, 68)` |
| Warning    | `#F59E0B` | `rgb(245, 158, 11)`|
| Info       | `#3B82F6` | `rgb(59, 130, 246)`|

### Typography

- **Sans-serif**: `Inter` (Primary font)
- **Serif**: `Playfair Display` (Headings)
- **Monospace**: `Fira Code` (Code blocks)

## 🚀 Deployment

### Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fyour-username%2Fstone-showcase&project-name=stone-showcase&repository-name=stone-showcase)

### Netlify

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/your-username/stone-showcase)

## 🤝 Contributing

Contributions are welcome! Here's how you can contribute:

1. Fork the repository
2. Create a new branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Message Guidelines

We use [Conventional Commits](https://www.conventionalcommits.org/) for commit messages. Please follow these guidelines:

- `feat:` A new feature
- `fix:` A bug fix
- `docs:` Documentation only changes
- `style:` Changes that do not affect the meaning of the code (white-space, formatting, etc.)
- `refactor:` A code change that neither fixes a bug nor adds a feature
- `perf:` A code change that improves performance
- `test:` Adding missing or correcting existing tests
- `chore:` Changes to the build process or auxiliary tools and libraries

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Vite](https://vitejs.dev/) for the amazing build tooling
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [React](https://reactjs.org/) for the component-based UI library
- [Three.js](https://threejs.org/) for 3D graphics
- [React Three Fiber](https://docs.pmnd.rs/react-three-fiber/getting-started/introduction) for React bindings for Three.js
- [Drei](https://github.com/pmndrs/drei) for useful Three.js helpers
- [Framer Motion](https://www.framer.com/motion/) for animations
