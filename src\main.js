"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var client_1 = require("react-dom/client");
var react_helmet_async_1 = require("react-helmet-async");
var react_router_dom_1 = require("react-router-dom");
var App_tsx_1 = require("./App.tsx");
require("./index.css");
// Error Boundary Component
var ErrorBoundary = /** @class */ (function (_super) {
    __extends(ErrorBoundary, _super);
    function ErrorBoundary(props) {
        var _this = _super.call(this, props) || this;
        _this.state = { hasError: false };
        return _this;
    }
    ErrorBoundary.getDerivedStateFromError = function () {
        return { hasError: true };
    };
    ErrorBoundary.prototype.componentDidCatch = function (error, errorInfo) {
        console.error('Error caught by ErrorBoundary:', error, errorInfo);
        // You can also log the error to an error reporting service
        // logErrorToService(error, errorInfo);
    };
    ErrorBoundary.prototype.render = function () {
        if (this.state.hasError) {
            return (<div className="min-h-screen flex items-center justify-center bg-primary p-4">
          <div className="max-w-md w-full text-center p-8 bg-secondary rounded-lg shadow-xl">
            <h1 className="text-2xl font-bold text-accent mb-4">Something went wrong</h1>
            <p className="mb-6">We're sorry for the inconvenience. Please try refreshing the page or contact support if the problem persists.</p>
            <button onClick={function () { return window.location.reload(); }} className="px-6 py-2 bg-accent text-primary font-medium rounded hover:bg-opacity-90 transition-colors">
              Refresh Page
            </button>
          </div>
        </div>);
        }
        return this.props.children;
    };
    return ErrorBoundary;
}(react_1.default.Component));
// Initialize the app
var container = document.getElementById('root');
if (!container)
    throw new Error('Failed to find the root element');
var root = (0, client_1.createRoot)(container);
root.render(<react_1.default.StrictMode>
    <ErrorBoundary>
      <react_helmet_async_1.HelmetProvider>
        <react_router_dom_1.BrowserRouter>
          <App_tsx_1.default />
        </react_router_dom_1.BrowserRouter>
      </react_helmet_async_1.HelmetProvider>
    </ErrorBoundary>
  </react_1.default.StrictMode>);
// Register service worker for PWA support
if ('serviceWorker' in navigator && import.meta.env.PROD) {
    window.addEventListener('load', function () {
        navigator.serviceWorker.register('/service-worker.js')
            .then(function (registration) {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
        })
            .catch(function (error) {
            console.error('ServiceWorker registration failed: ', error);
        });
    });
}
