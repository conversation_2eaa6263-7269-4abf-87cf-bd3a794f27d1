"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var vite_1 = require("vite");
var plugin_react_1 = require("@vitejs/plugin-react");
var vite_plugin_pwa_1 = require("vite-plugin-pwa");
var tailwindcss_1 = require("tailwindcss");
var autoprefixer_1 = require("autoprefixer");
var path_1 = require("path");
exports.default = (0, vite_1.defineConfig)(function (_a) {
    var mode = _a.mode;
    // Load env file based on `mode` in the current working directory
    var env = (0, vite_1.loadEnv)(mode, process.cwd(), '');
    return {
        // Base public path when served in production
        base: '/',
        // Plugins array
        plugins: [
            (0, plugin_react_1.default)(),
            (0, vite_plugin_pwa_1.VitePWA)({
                registerType: 'autoUpdate',
                includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg'],
                manifest: {
                    name: 'Stone Showcase',
                    short_name: 'Stone Showcase',
                    description: 'Premium Natural Stone & Ceramic Products',
                    theme_color: '#1A1A1A',
                    background_color: '#1A1A1A',
                    display: 'standalone',
                    icons: [
                        {
                            src: 'pwa-192x192.png',
                            sizes: '192x192',
                            type: 'image/png',
                        },
                        {
                            src: 'pwa-512x512.png',
                            sizes: '512x512',
                            type: 'image/png',
                        },
                    ],
                },
                workbox: {
                    clientsClaim: true,
                    skipWaiting: true,
                    runtimeCaching: [
                        {
                            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
                            handler: 'CacheFirst',
                            options: {
                                cacheName: 'google-fonts-cache',
                                expiration: {
                                    maxEntries: 10,
                                    maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
                                },
                                cacheableResponse: {
                                    statuses: [0, 200],
                                },
                            },
                        },
                        {
                            urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
                            handler: 'CacheFirst',
                            options: {
                                cacheName: 'gstatic-fonts-cache',
                                expiration: {
                                    maxEntries: 10,
                                    maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
                                },
                                cacheableResponse: {
                                    statuses: [0, 200],
                                },
                            },
                        },
                    ],
                },
            }),
        ],
        // CSS configuration
        css: {
            modules: {
                localsConvention: 'camelCaseOnly',
            },
            postcss: {
                plugins: [
                    tailwindcss_1.default,
                    autoprefixer_1.default,
                ],
            },
            preprocessorOptions: {
                scss: {
                    additionalData: "@import \"@/styles/variables.scss\";",
                },
            },
        },
        // Resolve configuration
        resolve: {
            alias: {
                '@': path_1.default.resolve(__dirname, './src'),
                '~': path_1.default.resolve(__dirname, './public'),
            },
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
        },
        // Server configuration
        server: {
            port: 4000,
            host: true,
            open: true,
            proxy: {
                '/api': {
                    target: process.env.VITE_API_URL || 'http://localhost:5000',
                    changeOrigin: true,
                    rewrite: function (path) { return path.replace(/^\/api/, ''); },
                },
            },
        },
        // Build configuration
        build: {
            outDir: 'dist',
            assetsDir: 'assets',
            sourcemap: mode !== 'production',
            minify: mode === 'production' ? 'terser' : false,
            terserOptions: {
                compress: {
                    drop_console: mode === 'production',
                    drop_debugger: mode === 'production',
                },
            },
            rollupOptions: {
                output: {
                    manualChunks: {
                        react: ['react', 'react-dom', 'react-router-dom'],
                        vendor: ['lodash', 'axios', 'date-fns'],
                    },
                },
            },
            chunkSizeWarningLimit: 1000, // in kBs
        },
        // Environment variables
        define: {
            'process.env': __assign({}, env),
            __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
        },
    };
});
