"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var framer_motion_1 = require("framer-motion");
var Contact = function () {
    var _a = (0, react_1.useState)({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
    }), formData = _a[0], setFormData = _a[1];
    var _b = (0, react_1.useState)(false), isSubmitting = _b[0], setIsSubmitting = _b[1];
    var _c = (0, react_1.useState)(null), submitStatus = _c[0], setSubmitStatus = _c[1];
    var handleChange = function (e) {
        var _a = e.target, name = _a.name, value = _a.value;
        setFormData(function (prev) {
            var _a;
            return (__assign(__assign({}, prev), (_a = {}, _a[name] = value, _a)));
        });
    };
    var handleSubmit = function (e) { return __awaiter(void 0, void 0, void 0, function () {
        var error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    e.preventDefault();
                    setIsSubmitting(true);
                    setSubmitStatus(null);
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    // In a real app, you would send this data to your backend
                    console.log('Form submitted:', formData);
                    // Simulate API call
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 1500); })];
                case 2:
                    // Simulate API call
                    _a.sent();
                    setSubmitStatus({
                        success: true,
                        message: 'Thank you for your message! We will get back to you soon.'
                    });
                    // Reset form
                    setFormData({
                        name: '',
                        email: '',
                        phone: '',
                        subject: '',
                        message: '',
                    });
                    return [3 /*break*/, 5];
                case 3:
                    error_1 = _a.sent();
                    setSubmitStatus({
                        success: false,
                        message: 'There was an error sending your message. Please try again later.'
                    });
                    return [3 /*break*/, 5];
                case 4:
                    setIsSubmitting(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    return (<div className="min-h-screen bg-primary pt-24 pb-12">
      <div className="container mx-auto px-4">
        <framer_motion_1.motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Have questions about our products or services? Reach out to our team and we'll get back to you as soon as possible.
          </p>
        </framer_motion_1.motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <framer_motion_1.motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5, delay: 0.2 }} className="bg-secondary rounded-lg p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6">Send Us a Message</h2>
            
            {submitStatus && (<div className={"p-4 mb-6 rounded-md ".concat(submitStatus.success ? 'bg-green-900/50 border border-green-700' : 'bg-red-900/50 border border-red-700')}>
                {submitStatus.message}
              </div>)}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-1">
                    Full Name <span className="text-red-500">*</span>
                  </label>
                  <input type="text" id="name" name="name" value={formData.name} onChange={handleChange} required className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="John Doe"/>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} required className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="<EMAIL>"/>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium mb-1">
                    Phone Number
                  </label>
                  <input type="tel" id="phone" name="phone" value={formData.phone} onChange={handleChange} className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="+****************"/>
                </div>
                
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium mb-1">
                    Subject <span className="text-red-500">*</span>
                  </label>
                  <select id="subject" name="subject" value={formData.subject} onChange={handleChange} required className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-accent focus:border-transparent">
                    <option value="">Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="quote">Request a Quote</option>
                    <option value="product">Product Information</option>
                    <option value="support">Customer Support</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-1">
                  Message <span className="text-red-500">*</span>
                </label>
                <textarea id="message" name="message" rows={5} value={formData.message} onChange={handleChange} required className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="How can we help you?"></textarea>
              </div>
              
              <div className="flex items-center">
                <button type="submit" disabled={isSubmitting} className={"px-6 py-3 rounded-md font-medium transition-colors ".concat(isSubmitting
            ? 'bg-gray-600 cursor-not-allowed'
            : 'bg-accent text-primary hover:bg-opacity-90')}>
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </div>
            </form>
          </framer_motion_1.motion.div>
          
          {/* Contact Information */}
          <framer_motion_1.motion.div initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5, delay: 0.4 }} className="space-y-8">
            <div className="bg-secondary rounded-lg p-8 shadow-lg">
              <h2 className="text-2xl font-semibold mb-6">Get in Touch</h2>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="bg-gray-700 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium">Our Location</h3>
                    <p className="text-gray-300">123 Stone Avenue, Suite 100<br />New York, NY 10001</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-gray-700 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium">Email Us</h3>
                    <p className="text-gray-300">
                      <a href="mailto:<EMAIL>" className="hover:text-accent transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                    <p className="text-gray-300">
                      <a href="mailto:<EMAIL>" className="hover:text-accent transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-gray-700 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium">Call Us</h3>
                    <p className="text-gray-300">
                      <a href="tel:+18005551234" className="hover:text-accent transition-colors">
                        +****************
                      </a>
                    </p>
                    <p className="text-gray-300 text-sm mt-1">Mon-Fri: 9:00 AM - 6:00 PM EST</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 pt-6 border-t border-gray-700">
                <h3 className="font-medium mb-4">Follow Us</h3>
                <div className="flex space-x-4">
                  {[
            { name: 'Facebook', icon: 'facebook', url: '#' },
            { name: 'Instagram', icon: 'instagram', url: '#' },
            { name: 'Pinterest', icon: 'pinterest', url: '#' },
            { name: 'Houzz', icon: 'houzz', url: '#' },
        ].map(function (social) { return (<a key={social.name} href={social.url} target="_blank" rel="noopener noreferrer" className="w-10 h-10 flex items-center justify-center bg-gray-700 rounded-full hover:bg-accent hover:text-primary transition-colors" aria-label={social.name}>
                      <span className="sr-only">{social.name}</span>
                      <i className={"fab fa-".concat(social.icon, " text-lg")}></i>
                    </a>); })}
                </div>
              </div>
            </div>
            
            {/* Map */}
            <div className="bg-secondary rounded-lg overflow-hidden shadow-lg">
              <div className="h-64 bg-gray-800 flex items-center justify-center">
                <div className="text-center p-4">
                  <svg className="w-12 h-12 mx-auto text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                  </svg>
                  <p className="text-gray-400">Interactive Map Location</p>
                </div>
                {/* In a real app, you would embed a Google Map or similar here */}
              </div>
              <div className="p-4">
                <h3 className="font-medium">Visit Our Showroom</h3>
                <p className="text-sm text-gray-300">We welcome visitors to our showroom by appointment.</p>
                <button className="mt-3 text-accent hover:underline text-sm font-medium">
                  Get Directions →
                </button>
              </div>
            </div>
          </framer_motion_1.motion.div>
        </div>
        
        {/* FAQ Section */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-center mb-8">Frequently Asked Questions</h2>
          
          <div className="max-w-3xl mx-auto space-y-4">
            {[
            {
                question: "What are your business hours?",
                answer: "Our showroom is open Monday through Friday from 9:00 AM to 6:00 PM EST. We're also available by appointment outside of these hours."
            },
            {
                question: "Do you offer free consultations?",
                answer: "Yes, we offer free in-person or virtual consultations to discuss your project and provide expert advice on material selection and design."
            },
            {
                question: "What is your return policy?",
                answer: "Due to the custom nature of our products, all sales are final. We encourage customers to order samples before making a purchase decision."
            },
            {
                question: "How long does delivery take?",
                answer: "Delivery times vary based on product availability and your location. Most standard items ship within 5-7 business days, while custom orders may take 2-4 weeks."
            },
            {
                question: "Do you provide installation services?",
                answer: "Yes, we work with a network of certified installers. We can recommend professionals in your area or help coordinate installation if you're working with your own contractor."
            },
        ].map(function (faq, index) { return (<framer_motion_1.motion.div key={index} initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.1 }} className="bg-secondary rounded-lg overflow-hidden">
                <button className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none" onClick={function (e) {
                var content = e.currentTarget.nextElementSibling;
                content.style.display = content.style.display === 'block' ? 'none' : 'block';
                var icon = e.currentTarget.querySelector('svg');
                if (icon) {
                    icon.style.transform = icon.style.transform === 'rotate(180deg)' ? 'rotate(0deg)' : 'rotate(180deg)';
                }
            }}>
                  <span className="font-medium">{faq.question}</span>
                  <svg className="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"/>
                  </svg>
                </button>
                <div className="px-6 pb-4 hidden">
                  <p className="text-gray-300">{faq.answer}</p>
                </div>
              </framer_motion_1.motion.div>); })}
          </div>
          
          <div className="text-center mt-10">
            <p className="text-gray-300 mb-4">Still have questions?</p>
            <a href="#contact-form" className="inline-block bg-accent text-primary font-medium py-3 px-8 rounded-md hover:bg-opacity-90 transition-colors">
              Contact Our Team
            </a>
          </div>
        </div>
      </div>
    </div>);
};
exports.default = Contact;
