"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_router_dom_1 = require("react-router-dom");
var framer_motion_1 = require("framer-motion");
// Mock product data - in a real app, this would come from an API
var mockProduct = {
    id: 1,
    name: 'Carrara Marble',
    category: 'natural-stone',
    price: 75.99,
    images: [
        '/images/carrara-marble-1.jpg',
        '/images/carrara-marble-2.jpg',
        '/images/carrara-marble-3.jpg',
    ],
    description: 'Classic white and gray marble with subtle veining, perfect for countertops and flooring.',
    longDescription: 'Our premium Carrara Marble is sourced directly from the finest quarries in Italy. Each slab is carefully selected for its distinctive veining and consistent color. Ideal for kitchen countertops, bathroom vanities, and flooring, this timeless stone adds elegance to any space.',
    specifications: {
        size: 'Varies by slab',
        thickness: '2cm, 3cm',
        finish: 'Polished, Honed',
        origin: 'Carrara, Italy',
        waterAbsorption: 'Low',
        mohsHardness: '3-4',
        recommendedUse: 'Countertops, Flooring, Wall Cladding'
    },
    colors: ['white', 'gray'],
    relatedProducts: [2, 3, 4] // IDs of related products
};
var ProductDetail = function () {
    var id = (0, react_router_dom_1.useParams)().id;
    var _a = (0, react_1.useState)(0), currentImage = _a[0], setCurrentImage = _a[1];
    var _b = (0, react_1.useState)(mockProduct), product = _b[0], setProduct = _b[1];
    var _c = (0, react_1.useState)(true), loading = _c[0], setLoading = _c[1];
    // In a real app, you would fetch the product data here
    (0, react_1.useEffect)(function () {
        // Simulate API call
        var timer = setTimeout(function () {
            setProduct(mockProduct);
            setLoading(false);
        }, 500);
        return function () { return clearTimeout(timer); };
    }, [id]);
    if (loading) {
        return (<div className="min-h-screen flex items-center justify-center bg-primary">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-accent"></div>
      </div>);
    }
    return (<div className="min-h-screen bg-primary pt-24 pb-12">
      <div className="container mx-auto px-4">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <react_router_dom_1.Link to="/" className="text-gray-300 hover:text-white">
                Home
              </react_router_dom_1.Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <react_router_dom_1.Link to="/products" className="text-gray-300 hover:text-white">
                  Products
                </react_router_dom_1.Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-gray-400">{product.name}</span>
              </div>
            </li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="bg-secondary rounded-lg overflow-hidden h-96">
              <img src={product.images[currentImage]} alt={"".concat(product.name, " - ").concat(currentImage + 1)} className="w-full h-full object-cover"/>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {product.images.map(function (image, index) { return (<button key={index} onClick={function () { return setCurrentImage(index); }} className={"h-20 rounded-md overflow-hidden ".concat(currentImage === index ? 'ring-2 ring-accent' : '')}>
                  <img src={image} alt={"".concat(product.name, " thumbnail ").concat(index + 1)} className="w-full h-full object-cover"/>
                </button>); })}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
            <div className="flex items-center mb-4">
              <div className="flex text-yellow-400">
                {__spreadArray([], Array(5), true).map(function (_, i) { return (<svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                    <path d="M10 1l2.5 6.5L20 8l-5 4.5 1.5 6.5L10 15.5 3.5 19 5 12.5 0 8l7.5-.5L10 1z"/>
                  </svg>); })}
              </div>
              <span className="ml-2 text-gray-400">(24 reviews)</span>
            </div>

            <p className="text-2xl font-bold text-accent mb-6">${product.price.toFixed(2)}</p>

            <p className="text-gray-300 mb-6">{product.longDescription}</p>

            <div className="mb-6">
              <h3 className="font-semibold mb-2">Colors:</h3>
              <div className="flex space-x-2">
                {product.colors.map(function (color, i) { return (<button key={i} className={"w-8 h-8 rounded-full border-2 ".concat(color === 'white' ? 'bg-white' : "bg-".concat(color, "-500"))} title={color}></button>); })}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <div className="flex items-center border border-gray-600 rounded-md overflow-hidden">
                <button className="px-4 py-2 text-xl hover:bg-gray-700">-</button>
                <span className="px-4 py-2">1</span>
                <button className="px-4 py-2 text-xl hover:bg-gray-700">+</button>
              </div>
              <button className="bg-accent text-primary font-medium py-2 px-6 rounded-md hover:bg-opacity-90 transition-colors">
                Add to Project
              </button>
            </div>

            {/* QR Code Modal */}

            <div className="border-t border-gray-700 pt-4">
              <h3 className="font-semibold mb-2">Specifications:</h3>
              <ul className="space-y-2 text-gray-300">
                {Object.entries(product.specifications).map(function (_a) {
            var key = _a[0], value = _a[1];
            return (<li key={key} className="flex justify-between">
                    <span className="text-gray-400 capitalize">{key}:</span>
                    <span>{value}</span>
                  </li>);
        })}
              </ul>
            </div>
          </div>
        </div>

        {/* Related Products */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {product.relatedProducts.map(function (relatedId) {
            var relatedProduct = products.find(function (p) { return p.id === relatedId; });
            if (!relatedProduct)
                return null;
            return (<react_router_dom_1.Link key={relatedId} to={"/products/".concat(relatedId)} className="group">
                  <framer_motion_1.motion.div className="bg-secondary rounded-lg overflow-hidden shadow-lg h-full" whileHover={{ y: -5 }}>
                    <div className="h-48 bg-gray-800 overflow-hidden">
                      <img src={relatedProduct.image} alt={relatedProduct.name} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"/>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-1 group-hover:text-accent transition-colors">
                        {relatedProduct.name}
                      </h3>
                      <p className="text-accent font-medium">${relatedProduct.price.toFixed(2)}</p>
                    </div>
                  </framer_motion_1.motion.div>
                </react_router_dom_1.Link>);
        })}
          </div>
        </div>
      </div>
    </div>);
};
exports.default = ProductDetail;
