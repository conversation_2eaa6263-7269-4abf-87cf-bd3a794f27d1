"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_router_dom_1 = require("react-router-dom");
var fa_1 = require("react-icons/fa");
var Footer = function () {
    var currentYear = new Date().getFullYear();
    return (<footer className="bg-secondary text-highlight py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="mb-8 md:mb-0">
            <h3 className="text-xl font-bold text-accent mb-4">StoneCraft</h3>
            <p className="text-sm text-gray-300 mb-4">
              Premium stone, ceramic, and stair stone products for your dream projects. 
              Bringing elegance and durability to every space.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                <fa_1.FaFacebook size={20}/>
              </a>
              <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                <fa_1.FaInstagram size={20}/>
              </a>
              <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                <fa_1.FaLinkedin size={20}/>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="mb-8 md:mb-0">
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <react_router_dom_1.Link to="/" className="text-gray-300 hover:text-accent transition-colors">
                  Home
                </react_router_dom_1.Link>
              </li>
              <li>
                <react_router_dom_1.Link to="/products" className="text-gray-300 hover:text-accent transition-colors">
                  Products
                </react_router_dom_1.Link>
              </li>
              <li>
                <react_router_dom_1.Link to="/visualization" className="text-gray-300 hover:text-accent transition-colors">
                  3D Visualization
                </react_router_dom_1.Link>
              </li>
              <li>
                <react_router_dom_1.Link to="/contact" className="text-gray-300 hover:text-accent transition-colors">
                  Contact
                </react_router_dom_1.Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="mb-8 md:mb-0">
            <h4 className="text-lg font-semibold mb-4">Contact Us</h4>
            <ul className="space-y-3">
              <li className="flex items-start space-x-3">
                <fa_1.FaMapMarkerAlt className="text-accent mt-1 flex-shrink-0"/>
                <span className="text-sm text-gray-300">
                  123 Stone Street, Building A<br />
                  New York, NY 10001, USA
                </span>
              </li>
              <li className="flex items-center space-x-3">
                <fa_1.FaPhone className="text-accent"/>
                <a href="tel:+1234567890" className="text-sm text-gray-300 hover:text-accent transition-colors">
                  +1 (234) 567-890
                </a>
              </li>
              <li className="flex items-center space-x-3">
                <fa_1.FaEnvelope className="text-accent"/>
                <a href="mailto:<EMAIL>" className="text-sm text-gray-300 hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Newsletter</h4>
            <p className="text-sm text-gray-300 mb-4">
              Subscribe to our newsletter for the latest updates and offers.
            </p>
            <form className="flex flex-col space-y-2">
              <input type="email" placeholder="Your email address" className="px-4 py-2 bg-primary border border-gray-600 rounded-md text-sm text-highlight focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent" required/>
              <button type="submit" className="bg-accent text-primary font-medium py-2 px-4 rounded-md hover:bg-opacity-90 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent">
                Subscribe
              </button>
            </form>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-700 mt-12 pt-6 text-center">
          <p className="text-sm text-gray-400">
            &copy; {currentYear} StoneCraft. All rights reserved.
          </p>
        </div>
      </div>
    </footer>);
};
exports.default = Footer;
