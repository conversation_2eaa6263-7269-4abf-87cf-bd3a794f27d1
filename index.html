<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Premium Stone & Ceramic Showcase - Discover our exclusive collection of natural stone and ceramic products" />
    <meta name="keywords" content="stone, ceramic, marble, granite, quartz, countertop, tile, natural stone, interior design, home renovation" />
    <meta name="author" content="Stone Showcase" />
    <meta name="theme-color" content="#1A1A1A" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet" />
    
    <title>Stone Showcase | Premium Natural Stone & Ceramic Products</title>
    
    <!-- Preload critical CSS -->
    <style id="critical-css">
      /* Critical CSS will be inlined here */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
      }
      
      /* Smooth scrolling for the whole document */
      html {
        scroll-behavior: smooth;
      }
      
      /* Focus styles for keyboard navigation */
      *:focus-visible {
        outline: 2px solid #B8860B;
        outline-offset: 2px;
      }
    </style>
  </head>
  <body class="bg-primary text-white font-sans antialiased leading-relaxed">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Load non-critical CSS asynchronously -->
    <link rel="stylesheet" href="/src/styles/global.css" media="print" onload="this.media='all'" />
    
    <!-- Fallback for browsers that don't support media query onload -->
    <noscript>
      <link rel="stylesheet" href="/src/styles/global.css" />
    </noscript>
  </body>
</html>
