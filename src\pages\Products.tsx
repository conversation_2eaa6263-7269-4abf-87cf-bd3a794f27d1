import { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

// Mock product data
const products = [
  {
    id: 1,
    name: 'Carrara Marble',
    category: 'natural-stone',
    price: 75.99,
    image: '/images/carrara-marble.jpg',
    description: 'Classic white and gray marble with subtle veining.',
    colors: ['white', 'gray']
  },
  {
    id: 2,
    name: 'Slate Tile',
    category: 'natural-stone',
    price: 45.50,
    image: '/images/slate-tile.jpg',
    description: 'Natural slate tiles with rich texture.',
    colors: ['black', 'gray', 'green']
  },
  // Add more products as needed
];

const Products = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-primary pt-24 pb-12">
      <div className="container mx-auto px-4">
        <h1 className="text-4xl font-bold mb-8 text-center">Our Products</h1>
        
        {/* Search and Filter */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <input
            type="text"
            placeholder="Search products..."
            className="px-4 py-2 bg-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <select
            className="px-4 py-2 bg-secondary rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="all">All Categories</option>
            <option value="natural-stone">Natural Stone</option>
            <option value="ceramic-tiles">Ceramic Tiles</option>
            <option value="stair-stone">Stair Stone</option>
          </select>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <motion.div
              key={product.id}
              className="bg-secondary rounded-lg overflow-hidden shadow-lg"
              whileHover={{ y: -5 }}
            >
              <Link to={`/products/${product.id}`}>
                <div className="h-48 bg-gray-800 overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-gray-300 mb-2">${product.price.toFixed(2)}</p>
                  <p className="text-gray-400 text-sm">{product.description}</p>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Products;
