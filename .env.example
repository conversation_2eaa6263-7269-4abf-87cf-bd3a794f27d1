# Environment variables template for the application
# Copy this file to .env and fill in the values

# Application
VITE_APP_NAME="Stone Showcase"
VITE_APP_DESCRIPTION="Premium Natural Stone & Ceramic Products"
VITE_APP_VERSION=0.1.0

# API Configuration
VITE_API_URL=http://localhost:5000/api
VITE_API_TIMEOUT=10000

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ANALYTICS_ID=""

# External Services
VITE_MAPBOX_TOKEN=your_mapbox_token_here
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key

# Environment (development, staging, production)
NODE_ENV=development

# Build Configuration
GENERATE_SOURCEMAP=true

# PWA Configuration
VITE_PWA_ENABLED=true
VITE_PWA_REGISTER_TYPE=autoUpdate

# Logging
VITE_LOG_LEVEL=debug
